interface Workplace {
  id: number;
  name: string;
  status: number;
}

interface Shift {
  id: number;
  createdAt: string;
  startAt: string;
  endAt: string;
  workplaceId: number;
  workerId: number | null;
  cancelledAt: string | null;
}

interface PaginatedResponse<T> {
  data: T[];
  links: { next?: string };
}

interface WorkplaceWithShifts {
  name: string;
  shifts: number;
}

const API_BASE_URL = "http://localhost:3000";

async function fetchAllWorkplaces(): Promise<Workplace[]> {
  const workplaces: Workplace[] = [];
  let nextUrl = `${API_BASE_URL}/workplaces`;

  while (nextUrl && nextUrl !== "") {
    const response = await fetch(nextUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch workplaces: ${response.status} ${response.statusText}`);
    }

    const data: PaginatedResponse<Workplace> = await response.json();
    workplaces.push(...data.data);

    nextUrl = data.links.next ? `${API_BASE_URL}${data.links.next}` : "";
  }

  return workplaces;
}

async function fetchAllShifts(): Promise<Shift[]> {
  const shifts: Shift[] = [];
  let nextUrl = `${API_BASE_URL}/shifts`;

  while (nextUrl && nextUrl !== "") {
    const response = await fetch(nextUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch shifts: ${response.status} ${response.statusText}`);
    }

    const data: PaginatedResponse<Shift> = await response.json();
    shifts.push(...data.data);

    nextUrl = data.links.next ? `${API_BASE_URL}${data.links.next}` : "";
  }

  return shifts;
}

async function getTopWorkplaces(): Promise<WorkplaceWithShifts[]> {
  try {
    // Fetch all workplaces and shifts
    const [workplaces, shifts] = await Promise.all([fetchAllWorkplaces(), fetchAllShifts()]);

    // Filter for active workplaces (status = 0)
    const activeWorkplaces = workplaces.filter((workplace) => workplace.status === 0);

    // Filter for completed shifts (workerId is not null and not cancelled)
    const completedShifts = shifts.filter(
      (shift) => shift.workerId !== null && shift.cancelledAt === null,
    );

    // Count completed shifts per active workplace
    const shiftCounts = new Map<number, number>();

    for (const shift of completedShifts) {
      const currentCount = shiftCounts.get(shift.workplaceId) || 0;
      shiftCounts.set(shift.workplaceId, currentCount + 1);
    }

    // Create result array with workplace names and shift counts
    const workplaceResults: WorkplaceWithShifts[] = activeWorkplaces.map((workplace) => ({
      name: workplace.name,
      shifts: shiftCounts.get(workplace.id) || 0,
    }));

    // Sort by shift count (descending) and take top 3
    return workplaceResults.sort((a, b) => b.shifts - a.shifts).slice(0, 3);
  } catch (error) {
    console.error("Error fetching top workplaces:", error);
    throw error;
  }
}

async function main() {
  try {
    const topWorkplaces = await getTopWorkplaces();
    console.log(JSON.stringify(topWorkplaces, null, 2));
  } catch (error) {
    console.error("Script failed:", error);
    process.exit(1);
  }
}

main();
